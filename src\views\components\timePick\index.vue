<template>
  <div>
    <ul class="timeUl">
      <li
        v-for="(item, index) in timelist"
        :key="index"
        @click="clickLi(index, item)"
        :class="curLi == index ? 'active' : ''"
      >
        {{ item.name }}
      </li>
      <div v-if="curLi == 5" style="line-height: 28px">
        <el-date-picker
          v-model="startTimePick"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择开始时间"
          :disabled="probationPeriod==0?true:false"
          @change="changeStartTime"
          :picker-options="
            probationPeriod == 3&&isProbation==1
              ? pickerOptionsThreeM
              : probationPeriod == 6&&isProbation==1
              ? pickerOptionsSixM
              : probationPeriod == 12&&isProbation==1
              ? pickerOptionsOneY
              : probationPeriod == 24&&isProbation==1
              ? pickerOptionsTwoY
              : probationPeriod == 36&&isProbation==1
              ? pickerOptionsThreeY
              : probationPeriod == 48&&isProbation==1
              ? pickerOptionsFourY
              : probationPeriod == 60&&isProbation==1
              ? pickerOptionsFiveY
              : probationPeriod == 999&&isProbation==1
              ? pickerOptionsMoreY
              : ''
          "
        ></el-date-picker
        > -
        <el-date-picker
          v-model="endTimePick"
          size="small"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择结束时间"
          :disabled="probationPeriod==0?true:false"
          @change="changeEndTime"
          :picker-options="
              probationPeriod == 3&&isProbation==1
              ? pickerOptionsThreeM
              : probationPeriod == 6&&isProbation==1
              ? pickerOptionsSixM
              : probationPeriod == 12&&isProbation==1
              ? pickerOptionsOneY
              : probationPeriod == 24&&isProbation==1
              ? pickerOptionsTwoY
              : probationPeriod == 36&&isProbation==1
              ? pickerOptionsThreeY
              : probationPeriod == 48&&isProbation==1
              ? pickerOptionsFourY
              : probationPeriod == 60&&isProbation==1
              ? pickerOptionsFiveY
              : probationPeriod == 999&&isProbation==1
              ? pickerOptionsMoreY
              : ''
          "
        ></el-date-picker>
        <el-tooltip
          class="item"
          effect="light"
          placement="top-start"
          popper-class="tip-class"
        >
          <div slot="content" style="line-height: 24px; font-size: 12px">
            试用账号{{
                    probationPeriod == 0&&isProbation==1
                      ? "无法查看当前数据":
                    probationPeriod == 3&&isProbation==1
                      ? "最多可查看三个月的数据"
                      : probationPeriod == 6&&isProbation==1
                      ? "最多可查看六个月的数据"
                      : probationPeriod == 12&&isProbation==1
                      ? "最多可查看一年的数据"
                      : probationPeriod == 24&&isProbation==1
                      ? "最多可查看两年的数据"
                      : probationPeriod == 36&&isProbation==1
                      ? "最多可查看三年的数据"
                      : probationPeriod == 48&&isProbation==1
                      ? "最多可查看四年的数据"
                      : probationPeriod == 60&&isProbation==1
                      ? "最多可查看五年的数据"
                      : probationPeriod == 999&&isProbation==1
                      ? "最多可查看全量的数据"
                      : ""
            }}, <br />查看更长时间数据需购买正式账号
          </div>
          <span class="title-name">
            <i
              v-if="isProbation == 1"
              class="el-icon-question icon-report"
              style="color: #e6a23c; margin-left: 10px; cursor: pointer"
            ></i>
          </span>
        </el-tooltip>
      </div>
    </ul>
  </div>
</template>

<script>
export default {
  name: "timePick",
  props: {
    timeRound: {
      type: Number,
      default: 1,
    },
    startTime: {
      type: String,
      default: "",
    },
    endTime: {
      type: String,
      default: "",
    },
    curLi: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      startTimePick: this.startTime,
      endTimePick: this.endTime,
      probationPeriod: "",
      isProbation: "",
      timelist: [
        { name: "今天", value: 0 },
        { name: "24小时", value: 1 },
        { name: "三天", value: 2 },
        { name: "七天", value: 3 },
        { name: "三十天", value: 4 },
        { name: "自定义", value: 5 },
      ],
      pickerOptionsZero: {
        disabledDate(date) {
            return date.getTime() < Date.now() - 8.64e7 && date.getTime() > Date.now() ;

        },
      },
      pickerOptionsThreeM: {
        disabledDate(time) {
          const threeMonth = new Date().setMonth(new Date().getMonth() - 3);
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsSixM: {
        disabledDate(time) {
          const threeMonth = new Date().setMonth(new Date().getMonth() - 6);
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsOneY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 1
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsTwoY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 2
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsThreeY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 3
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsFourY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 4
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsFiveY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 5
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
      pickerOptionsMoreY: {
        disabledDate(time) {
          const threeMonth = new Date().setFullYear(
            new Date().getFullYear() - 83
          );
          return (
            time.getTime() > new Date().getTime() || time.getTime() < threeMonth
          );
        },
      },
    };
  },
  created() {
    this.getTime();
  },
  watch: {
    startTime: {
      handler(newVal) {
        this.startTimePick = newVal;
      },
      immediate: true
    },
    endTime: {
      handler(newVal) {
        this.endTimePick = newVal;
      },
      immediate: true
    }
  },
  methods: {
    // 时间选择
    clickLi(index, item) {
      this.$emit("update:curLi", index);
      this.$emit("update:timeRound", item.value);
      if (item.value !== 5) {
        this.startTimePick = "";
        this.endTimePick = "";
        this.$emit("update:startTime", "");
        this.$emit("update:endTime", "");
      }
    },
    changeStartTime() {
      if (this.curLi == 5) {
        this.$emit("update:startTime", this.startTimePick);
      } else {
        this.$emit("update:startTime", "");
      }
    },
    changeEndTime() {
      if (this.curLi == 5) {
        this.$emit("update:endTime", this.endTimePick);
      } else {
        this.$emit("update:endTime", "");
      }
    },
    getTime() {
      this.$store.dispatch("GetInfo").then((res) => {
        // 试用时间范围
        this.probationPeriod = res.user.probationPeriod;
        this.isProbation = res.user.isProbation;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.timeUl {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 4px;
  li {
    margin-right: 16px;
    list-style: none;
    color: #606266;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    padding: 0 6px;
    border-radius: 3px;
  }
  li:first-child {
    display: none;
  }
  li.active {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
  li.activeErrorLi {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
}
</style>
