<template>
  <div class="home">
    <div class="homeBox msg-search">
      <h2 class="homeH2">发文统计</h2>
      <el-form :mode="queryForm" label-width="100px" @submit.native.prevent>
        <div class="search-words">
          <el-form-item label="关键词：" prop="word">
            <el-row :gutter="20">
              <el-col :span="6" class="slide-wrap">
                <el-input ref="myInput" v-model.trim="queryForm.word" clearable size="mini" @input="focusSlide"
                  @mouseup.stop.native="focusSlide" @keyup.enter.native="nowSearch(1)"></el-input>
                <transition name="el-zoom-in-top">
                  <div ref="addInput" v-show="slideFlag" class="slide-box">
                    <span class="slide-item" v-for="item in slideList" @mousedown="addSlide(item)">{{item}}</span>
                  </div>
                </transition>
              </el-col>
              <el-col :span="10">
                <el-button type="primary" @click="nowSearch(1)" size="mini">开始搜索</el-button>
                <el-popover placement="top-start" width="500" trigger="hover">
                  <div>
                    1："+"表示"并且","|"表示"或" <br />2：什么情况下用"|"：如想关注北京或上海或广州的新闻，表达式为"北京|上海|广州"，表示文章中出现
                    "北京"、"上海"、"广州"任意一个城市就能监测到。
                    <br />3：什么情况下用"+"：如想关注北京车牌摇号的新闻，表达式为"北京+车牌摇号"，表示文章中同时出现 "北京"和"车牌摇号"两个关键词才能监测到。
                    <br /> 4：什么情况下同时用到"+"、"|"：如想关注上海世博会的新闻，由于"世博会"又可能被称为"世界博览会"，表达式为
                    "上海+(世博会|世界博览会)"，表示文章中出现"上海"，同时出现"世博会"或者 "世界博览会"中任意一个词，就能监测到；
                  </div>
                  <i class="el-icon-question" slot="reference"></i>
                </el-popover>
                <el-button size="mini" @click="higTools" v-show="false">高级工具</el-button>
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <div class="search-words" v-show="showSecond">
          <el-form-item label="检索词：" prop="quadraticWord">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-input v-model="queryForm.quadraticWord" clearable size="mini" placeholder="多个检索词使用空格分开"
                  @keyup.enter.native="nowSearch(1)"></el-input>
              </el-col>
              <el-col :span="1">
                <!-- <i @click="showSecond=false"></i> -->
                <img class="el-icon-minus" src="../../../assets/images/iconMinus.png" alt="" @click="showSecond=false">
              </el-col>
            </el-row>
          </el-form-item>
        </div>
        <el-row>
          <el-col :span="6">
            <el-form-item label="检索模式：" prop="accurate">
              <ul class="timeUl">
                <li v-for="(item, index) in matchData" @click="clickLi(1, index, item.value)"
                  :class="curLiOne == index ? 'active' : ''" :key="index">
                  {{ item.name }}
                </li>
              </ul>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="信源类型：" prop="mediaType">
              <ul class="timeUl">
                <li v-for="(item, index) in medTypeData" @click="clickLi(4, index, item.value)"
                  :class="curliFour == index ? 'active' : ''" :key="index">
                  {{ item.name }}
                </li>
              </ul>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="匹配方式：" prop="position">
              <ul class="timeUl">
                <li v-for="(item, index) in keyWordData" @click="clickLi(3, index, item.value)"
                  :class="curLiThree == index ? 'active' : ''" :key="index">
                  {{ item.name }}
                </li>
              </ul>
            </el-form-item>
          </el-col>
          <el-col :span="18">
            <el-form-item label="时间范围：" prop="timeRound">
              <timePick :timeRound.sync="queryForm.timeRound" :startTime.sync="queryForm.startTime"
                :endTime.sync="queryForm.endTime" :curLi.sync="curliFive"></timePick>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="媒体账号：">
              <SitePage style="margin-right: 20px;" ref="sitePage" :mediaType="queryForm.mediaType"
                @checkedIds="getCheckedIds"></SitePage>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 发文统计 -->
    <div class="statics-wrap">
      <div class="statics-title">
        <span>共 <em>{{staticList?staticList.length:0}}</em> 个账号数据</span>
        <el-button :disabled="accountNameList.length==0" style="margin:10px 0;"
          @click="exportDocument">导出excel</el-button>
      </div>
      <el-table v-loading="staticLoad" border :data="staticList" :header-cell-style="{background:'#ddebf7'}"
        :row-class-name="tableAddClass">
        <el-table-column width="65" label="序号" align="center" type="index"></el-table-column>
        <el-table-column label="媒体帐号" align="center" prop="name" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="发文数" align="center" prop="count" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="操作" align="center" prop="option">
          <template slot-scope="scope">
            <el-button type="text" @click="goDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 高级搜索 -->
    <el-dialog title="高级检索" width="600px" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-width="130px">
        <el-form-item label="包含地域关键词：" prop="keywordOne">
          <el-input v-model.trim="form.keywordOne" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="包含人物关键词：" prop="keywordTwo">
          <el-input v-model.trim="form.keywordTwo" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="包含事件关键词：" prop="keywordThree">
          <el-input v-model.trim="form.keywordThree" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="不含以下关键词：" prop="keywordFour">
          <el-input v-model.trim="form.keywordFour" type="textarea"></el-input>
        </el-form-item>
        <el-form-item label="关键词所在位置：" prop="position">
          <el-checkbox-group v-model.trim="form.position">
            <el-checkbox v-for="(city, index) in keyWordDataTwo" :label="city.value" :value="city.value" :key="index">{{
              city.name }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="keyWorddialog">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  documentStatistic, exportStatistic
} from "@/api/monitor/systemRetrieval";
import { timeJson } from "@/utils/time.js";
import timePick from "@/views/components/timePick";
import SitePage from "./SitePage.vue"
export default {
  name: "documentStatistic",
  components: {
    timePick, SitePage
  },
  // 解决：菜单之间跳转，清除缓存。跳转到子页面在回来，缓存还在
  beforeRouteLeave(to, from, next) {
    console.log("to", to);
    if (to.path == "/conentReview/infoDetailPage/index") {
      // 缓存页面
      let view = { meta: { noCache: false }, name: "documentStatistic" };
      this.$store.dispatch("tagsView/addCachedView", view).then(() => {
        next();
      });
    } else {
      // 清除缓存页面
      this.$store
        .dispatch("tagsView/delCachedView", {
          meta: { noCache: false },
          name: "documentStatistic",
        })
        .then(() => {
          next();
        });
    }
  },
  data() {
    return {
      slideList: ['+', '|', '(', ')'],
      slideFlag: false,
      showSecond: false,
      staticLoad: false,
      accountNameList: [],
      staticList: [],
      isProbation: "",
      probationPeriod: "",
      dialogFormVisible: false,
      form: {
        keywordOne: "",
        keywordTwo: "",
        keywordThree: "",
        keywordFour: "",
        position: ["1"],
      },
      queryForm: {
        sort: "1",
        position: "0",
        accurate: 1,
        word: "",
        timeRound: 1,
        mediaType: "5",
        startTime: "",
        endTime: "",
        quadraticWord: ""
      },

      matchData: [
        { name: "精准检索", value: "1" },
        { name: "模糊检索", value: "0" },
      ],
      keyWordData: [
        { name: "不限", value: "0" },
        { name: "标题中", value: "1" },
        { name: "正文中", value: "2" },
      ],
      keyWordDataTwo: [
        { name: "标题中", value: "1" },
        { name: "正文中", value: "2" },
      ],
      // 去除-1和0的medTypeData
      medTypeData: [
        { name: "微信", value: "5" },
        { name: "微博", value: "3" },
        { name: "抖音", value: "111" },
        { name: "快手", value: "112" },
        { name: "b站", value: "113" },
        { name: "今日头条", value: "61" },
      ],
      curLiOne: 0,
      curLiThree: 0,
      curliFour: 0,
      curliFive: 0,
    };
  },
  created() {
    this.getTime();
  },
  mounted() {
    this.curliFive = 1
    this.clickLi(4, 0, '5')
    this.nowSearch(1);
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.slideFlag = false;
        }
      }
    });
  },
  beforeDestroy() {
    // 在组件销毁前移除事件监听器
    document.removeEventListener('mouseup', this.showSlide);
  },
  methods: {
    // 插入文字
    insertTextAtCursor(text) {
      const input = this.$refs.myInput.$el.querySelector('input');
      if (document.activeElement === input) {
        // 获取光标位置
        const startPos = input.selectionStart;
        const endPos = input.selectionEnd;
        // 插入文字
        const newValue =
          input.value.substring(0, startPos) +
          text +
          input.value.substring(endPos, input.value.length);
        // 更新输入框的值
        this.queryForm.word = newValue;
        // 如果你希望输入框保持焦点并且光标在正确的位置，你可能需要使用 $nextTick
        setTimeout(() => {
          input.focus();
          input.setSelectionRange(startPos + text.length, startPos + text.length);
        }, 0);
      }
    },
    addSlide(item) {
      this.insertTextAtCursor(item)
    },
    focusSlide() {
      if (this.queryForm.word) {
        this.slideFlag = true
      }
    },
    showSlide(val) {
      if (val) {
        this.slideFlag = true
      }
    },
    hideSlide() {
      this.slideFlag = false
    },
    // 获取发文统计
    async queryDocumentStatistic() {
      if (this.curliFive == 5) {
        if (
          this.queryForm.startTime &&
          this.queryForm.endTime &&
          this.queryForm.startTime < this.queryForm.endTime
        ) {
          let params = {
            sort: this.queryForm.sort,
            position: this.queryForm.position,
            accurate: this.queryForm.accurate,
            word: this.queryForm.word,
            quadraticWord: this.queryForm.quadraticWord,
            timeRound: this.queryForm.timeRound,
            mediaType: this.queryForm.mediaType,
            startTime: this.queryForm.startTime,
            endTime: this.queryForm.endTime,
            accountName: this.accountNameList
          };
          this.staticLoad = true
          let res = await documentStatistic(params);
          if (res.code == 500) {
            this.$message.error(res.msg)
          }
          this.staticLoad = false
          this.staticList = res.rows;
        } else {
          this.$message.error("选择时间自定义时间范围");
        }
      } else {
        let params = {
          sort: this.queryForm.sort,
          position: this.queryForm.position,
          accurate: this.queryForm.accurate,
          word: this.queryForm.word,
          quadraticWord: this.queryForm.quadraticWord,
          timeRound: this.queryForm.timeRound,
          mediaType: this.queryForm.mediaType,
          accountName: this.accountNameList
        };
        this.staticLoad = true
        let res = await documentStatistic(params);
        if (res.code == 500) {
          this.$message.error(res.msg)
        }
        this.staticLoad = false
        this.staticList = res.rows;
      }
    },
    // 导出发文统计
    exportDocument() {
      this.$confirm('是否确认导出所有发文统计?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        if (this.curliFive == 5) {
          if (
            this.queryForm.startTime &&
            this.queryForm.endTime &&
            this.queryForm.startTime < this.queryForm.endTime
          ) {
            let params = {
              sort: this.queryForm.sort,
              position: this.queryForm.position,
              accurate: this.queryForm.accurate,
              word: this.queryForm.word,
              quadraticWord: this.queryForm.quadraticWord,
              timeRound: this.queryForm.timeRound,
              mediaType: this.queryForm.mediaType,
              startTime: this.queryForm.startTime,
              endTime: this.queryForm.endTime,
              accountName: this.accountNameList
            };
            exportStatistic(params).then((res) => {
              this.download(res.msg);
              this.msgSuccess("导出成功");
            });
          } else {
            this.$message.error("选择时间自定义时间范围");
          }
        } else {
          let params = {
            sort: this.queryForm.sort,
            position: this.queryForm.position,
            accurate: this.queryForm.accurate,
            word: this.queryForm.word,
            quadraticWord: this.queryForm.quadraticWord,
            timeRound: this.queryForm.timeRound,
            mediaType: this.queryForm.mediaType,
            accountName: this.accountNameList
          }
          exportStatistic(params).then((res) => {
            this.download(res.msg);
            this.msgSuccess("导出成功");
          });
        }
      })
    },
    // 发文数为0 显示红色字体
    tableAddClass({ row, rowIndex }) {
      if (row.count == 0) {
        return 'tr-red';
      }
      return '';
    },
    // 获取所选站点
    getCheckedIds(val) {
      this.accountNameList = val
    },
    getTime() {
      this.$store.dispatch("GetInfo").then((res) => {
        this.isProbation = res.user.isProbation;
        this.probationPeriod = res.user.probationPeriod;
      });
    },
    // 精确度选择
    clickLi(curVal, index, value) {
      if (curVal == 1) {
        this.curLiOne = index;
        this.queryForm.accurate = value;
      } else if (curVal == 3) {
        this.curLiThree = index;
        this.queryForm.position = value;
      } else if (curVal == 4) {
        this.$refs.sitePage.resetCheck() // 重置站点
        this.curliFour = index;
        this.queryForm.mediaType = value;
      }
    },
    // 高级工具
    higTools() {
      this.dialogFormVisible = true;
    },
    // 确定高级工具弹框
    keyWorddialog() {
      if (this.form.position.length > 0) {
        if (this.form.position.length == 1) {
          if (this.form.position[0] == 1) {
            let str = "";
            if (this.form.keywordOne != "" && this.form.keywordOne != null) {
              str += " AND " + this.changeString(this.form.keywordOne, "title");
            }
            if (this.form.keywordTwo != "" && this.form.keywordTwo != null) {
              str += " AND " + this.changeString(this.form.keywordTwo, "title");
            }
            if (
              this.form.keywordThree != "" &&
              this.form.keywordThree != null
            ) {
              str +=
                " AND " + this.changeString(this.form.keywordThree, "title");
            }
            if (str != "") {
              str = str.substring(4, str.length);
            }
            if (this.form.keywordFour != "" && this.form.keywordFour != null) {
              str += " - " + this.changeString(this.form.keywordFour, "title");
            }
            this.queryForm.word = str;
          } else {
            //   正文
            let str = "";
            if (this.form.keywordOne != "" && this.form.keywordOne != null) {
              str += " AND " + this.changeString(this.form.keywordOne, "text");
            }
            if (this.form.keywordTwo != "" && this.form.keywordTwo != null) {
              str += " AND " + this.changeString(this.form.keywordTwo, "text");
            }
            if (
              this.form.keywordThree != "" &&
              this.form.keywordThree != null
            ) {
              str +=
                " AND " + this.changeString(this.form.keywordThree, "text");
            }
            if (str != "") {
              str = str.substring(4, str.length);
            }
            if (this.form.keywordFour != "" && this.form.keywordFour != null) {
              str += " - " + this.changeString(this.form.keywordFour, "text");
            }
            this.queryForm.word = str;
          }
        } else {
          //   标题+正文
          let str = "";
          if (this.form.keywordOne != "" && this.form.keywordOne != null) {
            str += " AND " + this.changeString(this.form.keywordOne, "all");
          }
          if (this.form.keywordTwo != "" && this.form.keywordTwo != null) {
            str += " AND " + this.changeString(this.form.keywordTwo, "all");
          }
          if (this.form.keywordThree != "" && this.form.keywordThree != null) {
            str += " AND " + this.changeString(this.form.keywordThree, "all");
          }
          if (str != "") {
            str = str.substring(4, str.length);
          }
          if (this.form.keywordFour != "" && this.form.keywordFour != null) {
            str += " - " + this.changeString(this.form.keywordFour, "all");
          }
          this.queryForm.word = str;
        }
      }
      this.dialogFormVisible = false;
      this.form.position = ["1"];
      this.form.keywordOne = "";
      this.form.keywordTwo = "";
      this.form.keywordThree = "";
    },
    changeString(val, name) {
      if (val) {
        return name + ":(" + '"' + val.replace(/\s+/g, '" "') + '"' + ")";
      } else {
        return "";
      }
    },

    // 搜索
    async nowSearch(val) {
      if (this.showSecond && !this.queryForm.word) {
        this.$message.error('请输入关键词！')
        return
      }
      if (val == 1) {
        this.queryForm.pageNum = 1
      }

      // 检查是否选择了媒体账号
      if (this.accountNameList.length == 0) {
        this.$message.error('请选择媒体账号！')
        return false
      }

      this.queryDocumentStatistic()
    },
    goDetail(row) {
      const newRoute = this.$router.resolve({
        path: '/monitor/systemRetrieval/detail',
        query: {
          name: row.name,
          
          // sort: this.queryForm.sort,
          position: this.queryForm.position,
          accurate: this.queryForm.accurate,
          word: this.queryForm.word,
          // quadraticWord: this.queryForm.quadraticWord,
          timeRound: this.queryForm.timeRound,
          mediaType: this.queryForm.mediaType,
          startTime: this.queryForm.startTime,
          endTime: this.queryForm.endTime,
        }
      });
      window.open(newRoute.href, "_blank");
    },
  },
};
</script>
<style>
.el-message-box__header {
  border-bottom: solid 1px #ccc;
}
</style>

<style scoped lang="scss">
.list-img {
  cursor: pointer;
  width: 16px;
  margin-left: 3px;
  position: relative;
  top: 10px;
  right: -126px;
}

.slide-wrap {
  position: relative;
}

.slide-box {
  position: absolute;
  top: 28px;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 100;
  margin-top: 10px;
  padding: 20px 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
}

.slide-item {
  margin-right: 15px;
  padding: 4px 10px;
  border-radius: 5px;
  background: #1890ff;
  color: #fff;
  cursor: pointer;
}

.el-icon-question {
  margin-left: 4px;
  font-size: 22px;
  vertical-align: middle;
  cursor: pointer;
}

.statics-wrap {
  width: 100%;
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 5px;
}

.statics-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: #515a6e;
  font-weight: bold;

  em {
    font-style: normal;
    color: #3d9ffe;
  }
}

.home {
  width: 100%;
  overflow: hidden;
  background: #f4f7fb;
  padding: 20px 5%;
  box-sizing: border-box;

  .homeBox {
    width: 100%;
    overflow: hidden;
    background: #fff;
    padding: 20px 0px;
    box-sizing: border-box;
    margin-bottom: 20px;

    .homeH2 {
      font-size: 18px;
      border-left: solid 3px #3d9ffe;
      margin-left: 25px;
      font-weight: normal;
      padding-left: 10px;
    }
  }
}

.timeUl {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 4px;

  li {
    margin-right: 16px;
    list-style: none;
    color: #606266;
    cursor: pointer;
    height: 28px;
    line-height: 28px;
    padding: 0 6px;
    border-radius: 3px;
  }

  li.active {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }

  li.activeErrorLi {
    border: solid 1px #3d9ffe;
    color: #3d9ffe;
  }
}

.search-words {
  position: relative;

  .el-col-6 {
    padding: 0 !important;
  }

  .el-icon-plus {
    color: #1890ff;
    cursor: pointer;
    height: 27px;
  }

  .el-icon-minus {
    color: #ed2121;
    cursor: pointer;
    height: 27px;
  }
}
</style>
