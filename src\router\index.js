import Vue from 'vue'
import Router from 'vue-router'
import store from '../store'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'
import ParentView from '@/components/ParentView';

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
  }
 */

// 公共路由
export const constantRoutes = [
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [{
            path: '/redirect/:path(.*)',
            component: (resolve) => require(['@/views/redirect'], resolve)
        }]
    },
    // {
    //     path: '/',
    //     redirect: "/login",
    //     hidden: true
    // },
    {
        path: '/sysupgrade',
        component: (resolve) => require(['@/views/sysupgrade'], resolve),
        hidden: true
    },
    {
        path: '/login',
        component: (resolve) => require(['@/views/login'], resolve),
        hidden: true
    },
    {
        path: '/wechatPhone',
        component: (resolve) => require(['@/views/wechatPhone'], resolve),
        hidden: true
    },
    // 微信消息推送列表
    {
        path: '/wechatList',
        component: (resolve) => require(['@/views/wechatList'], resolve),
        hidden: true
    },
    // 微信消息推送列表详情
    {
        path: '/wechatListDetail',
        component: (resolve) => require(['@/views/wechatListDetail'], resolve),
        hidden: true
    },
    // 微信消息推送绑定
    {
        path: '/wechatAlert',
        component: (resolve) => require(['@/views/wechatAlert'], resolve),
        hidden: true
    },
    {
        path: '/404',
        component: (resolve) => require(['@/views/error/404'], resolve),
        hidden: true
    },
    {
        path: '/401',
        component: (resolve) => require(['@/views/error/401'], resolve),
        hidden: true
    },
    //
    {
        path: '/singleLogin',
        component: (resolve) => require(['@/views/singleLogin/index'], resolve),
        hidden: true
    },
    {
        path: '/textFrame',
        component: (resolve) => require(['@/views/textFrame/index'], resolve),
        hidden: true
    },
    // 配置首页静态路由，默认输入/重定向到首页
    // 但会引起问题，会有一个首页，一个文稿审校 ，所以首页为文稿审校的菜单不配置
    {
        path: '/',
        component: Layout,
        redirect: "/index",
        children: [{
            path: "/index",
            // path: `${sessionStorage.getItem('idUrl')}/index`,
            component: (resolve) => {
                if (sessionStorage.getItem("setloginway") == 'normalLogin') {
                    // 普通用户
                    require([`@/views/index`], resolve)
                } else if (sessionStorage.getItem("setloginway") == 'textCheckLogin') {
                    // 文本审校用户，不展示首页
                    require([`@/views/textReview`], resolve)
                } else {
                    require([`@/views/index`], resolve)
                }
            },
            name: '首页',
            meta: { title: '首页', noCache: true, affix: true }
        }]
    },
    {
        path: '/user',
        component: Layout,
        hidden: true,
        redirect: 'noredirect',
        children: [{
            path: 'profile',
            component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
            name: 'Profile',
            meta: { title: '个人中心', icon: 'user' }
        }]
    },
    {
        path: '/dict',
        component: Layout,
        hidden: true,
        children: [{
            path: 'type/data/:dictId(\\d+)',
            component: (resolve) => require(['@/views/system/dict/data'], resolve),
            name: 'Data',
            meta: { title: '字典数据', icon: '' }
        }]
    },
    {
        path: '/job',
        component: Layout,
        hidden: true,
        children: [{
            path: 'log',
            component: (resolve) => require(['@/views/system/job/log'], resolve),
            name: 'JobLog',
            meta: { title: '调度日志' }
        }]
    },
    {
        path: '/gen',
        component: Layout,
        hidden: true,
        children: [{
            path: 'edit/:tableId(\\d+)',
            component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
            name: 'GenEdit',
            meta: { title: '修改生成配置' }
        }]
    },
    {
        path: "/conentReview",
        component: Layout,
        hidden: true,
        children: [{
            path: 'themMangeChase',
            component: (resolve) => require(['@/views/conentReview/themMangeChase'], resolve),
            name: '',
            meta: { title: '追详情页' }
        }, {
            path: 'detailPage/:id',
            component: (resolve) => require(['@/views/conentReview/detailPage'], resolve),
            name: 'detailPage',
            meta: { title: '' }
        }, {
            path: 'infoDetailPage/:id',
            component: (resolve) => require(['@/views/conentReview/infoDetailPage'], resolve),
            name: '',
            meta: { title: '' }
        }, {
            path: 'indexDetail2/:id',
            component: (resolve) => require(['@/views/conentReview/indexDetail/index2.vue'], resolve),
            name: '',
            meta: { title: '' }
        }, {
            path: 'indexDetail/:id',
            component: (resolve) => require(['@/views/conentReview/indexDetail'], resolve),
            name: '',
            meta: { title: '' }
        },  {
            path: 'indexList/:id',
            component: (resolve) => require(['@/views/conentReview/indexList'], resolve),
            name: 'indexList',
            meta: { title: '' }
        }]
    },
    {
        // 报告预览页面
        path: '/indexPageSee',
        component: (resolve) => require(['@/views/conentReview/indexPageSee'], resolve),
        name: '',
        hidden: true,
        meta: { title: '' }
    },
    {
        path: '/monitor',
        component: Layout,
        hidden: true,
        children: [
            {
                // 检索
                path: 'systemRetrieval/detail',
                component: (resolve) => require(['@/views/monitor/systemRetrieval/index'], resolve),
                name: '',
                hidden: true,
                meta: { title: '检索' }
            }
        ]
        
    }
]

var url = document.location.pathname;

var index = url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).substring(0, url.substr(url.indexOf('/', url.indexOf('/') - 1) + 1).indexOf('/'));

export default new Router({
    mode: 'history', // 去掉url中的#
    // base: `${sessionStorage.getItem('idUrl')}`,
    base: index,
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
})
