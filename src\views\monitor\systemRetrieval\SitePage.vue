<template>
    <div>
        <div class="site-check" v-loading="loading" v-show="!loading">
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
                <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
                  <el-tab-pane :label="`'${index}'`" v-for="(first,index) in newList" :key="index">
                    <div slot="label" style="display:flex">
                      <el-checkbox-group v-model="checkedKeys" class="son-menu" @change="handleCheckedCitiesChange(1,checkedKeys,first)">
                  <!-- 子菜单-->
                  <el-checkbox
                    :key="first.name"
                    :indeterminate="first.isIndeterminate" :label="first.name"
                  >
                      {{first.accountName}}
                  </el-checkbox>
                </el-checkbox-group>{{first.name}}</div>
                <div class="mag-wrap">
                  <div v-for="(second,indexs) in first.webSiteAndMediaAccountList" :key="indexs" class="msg-group">
                    <el-checkbox-group v-model="checkedKeys" class="check-item" @change="handleCheckedCitiesChange(2,checkedKeys,first,second,second.name)">
                      <el-checkbox :key="second.name" :label="second.name"> {{ second.name }}</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
                </el-tab-pane>
                </el-tabs>
            </div>
        <div v-loading="loading" v-show="loading" style="height:300px;width:100%;background:#fff;"></div>
    </div>
</template>
<script>
import {siteTypeTreeStatistic} from "@/api/index";
export default {
    data() {
        return {
            newList:[],
            secondName:[],
            filterLength:[],
            activeName:'',
            checkAll:false,
            isIndeterminate:false,
            checkedKeys:[],
            loading:false
        }
    },
    props:{
        mediaType:{
            type:String,
            default:'3'
        }
    },
    watch:{
        mediaType(){
            this.queryMediaSite()
        }
    },
    methods:{
        // 获取站点
        queryMediaSite(){
            if(this.mediaType=='3'||this.mediaType=='5'||this.mediaType=='61'||this.mediaType=='111'||this.mediaType=='112'||this.mediaType=='113'){
                this.loading = true
                siteTypeTreeStatistic({type:1,mediaType:this.mediaType}).then((res)=>{
                    this.loading = false
                    this.newList = res.data
                    this.newList.map((item) => {
                        this.filterLength.push(item.id)
                        this.secondName.push(item.nickName)
                        item.webSiteAndMediaAccountList.map((itemb) => {
                            this.filterLength.push(itemb.id)
                        })
                    })
                })
            }
        },
        // 处理站点数组
        setCheckList(checkedIds,newList){
            let checkedNickName = []
            newList.map((item)=>{
                item.webSiteAndMediaAccountList.map((itemb)=>{
                    if(checkedIds.includes(itemb.name)){
                        checkedNickName.push(itemb.nickName)
                    }
                })
            })
            let nickName = [...new Set(checkedNickName)]
            return nickName
        },
        handleClick(val){
            console.log(this.activeName);
        },
        // 全选信源分组
handleCheckAllChange() {
    this.isIndeterminate = false
    if(this.newList[0].webSiteAndMediaAccountList.length>=2000){
        this.loading = true
        setTimeout(()=>{
            this.loading = false
        },2000)
        setTimeout(()=>{
            if (this.checkAll) {
            this.newList.map((first) => {
                first.isIndeterminate = false
                const result = this.checkedKeys.includes(first.name)
                if (!result) {
                this.checkedKeys.push(first.name)
                }
                first.webSiteAndMediaAccountList.map((second) => {
                const results = this.checkedKeys.includes(second.name)
                if (!results) {
                    this.checkedKeys.push(second.name)
                }
                })
            })
            } else {
                this.newList.map((first) => {
                    first.isIndeterminate = false
                })
                this.checkedKeys = []
            }
            this.$emit('checkedIds', this.setCheckList(this.checkedKeys,this.newList))
        },500)
    }else{
        if (this.checkAll) {
            this.newList.map((first) => {
                first.isIndeterminate = false
                const result = this.checkedKeys.includes(first.name)
                if (!result) {
                this.checkedKeys.push(first.name)
                }
                first.webSiteAndMediaAccountList.map((second) => {
                const results = this.checkedKeys.includes(second.name)
                if (!results) {
                    this.checkedKeys.push(second.name)
                }
                })
            })
            } else {
                this.newList.map((first) => {
                    first.isIndeterminate = false
                })
                this.checkedKeys = []
            }
            this.$emit('checkedIds', this.setCheckList(this.checkedKeys,this.newList))
    }
},
        // 信源组筛选
        handleCheckedCitiesChange(type, checkedKey, first, second, id) {
        if (type == 1) {
            // 一级
            first.isIndeterminate = false
            if(this.newList[0].webSiteAndMediaAccountList.length>=2000){
                this.loading = true
                setTimeout(()=>{
                    this.loading = false
                },1500)
                setTimeout(() => {
                    this.handleNavigationOne(checkedKey, first)
                }, 500);
            }else{
                this.handleNavigationOne(checkedKey, first)
            }
        } else if (type == 2) {
            if(this.newList[0].webSiteAndMediaAccountList.length>=2000){
                this.loading = true
                setTimeout(()=>{
                    this.loading = false
                },1500)
                    // 二级
                setTimeout(() => {
                    this.handleNavigationTwo(checkedKey, first, second, id)
                },500)
            }else{
                this.handleNavigationTwo(checkedKey, first, second, id)
            }

        }
        },
    handleNavigationOne(checkedKey, first) {
        const result = checkedKey.includes(first.name)
        if (result) {
        first.webSiteAndMediaAccountList.forEach((d) => {
            const results = checkedKey.includes(d.name)
            if (!results) {
            this.checkedKeys.push(d.name)
            }
        })
        } else {
        for (let i = 0, len = first.webSiteAndMediaAccountList.length; i < len; i++) {
            // 存在选择中菜单的数据 进行删除, 反之 不进行操作
            for (let j = 0, lens = this.checkedKeys.length; j < lens; j++) {
            // 进行是否存在的判断
            const results = this.checkedKeys[j] === (first.webSiteAndMediaAccountList[i].name)
            if (results) {
                this.checkedKeys.splice(j, 1)
            }
            }
        }
        }
        if (this.checkedKeys.length == 0) {
            this.checkAll = false
        }else if(this.checkedKeys.length==this.filterLength.length){
            this.checkAll = true
        }
        this.isIndeterminate = this.checkedKeys.length > 0 && this.checkedKeys.length < this.filterLength.length
        this.$emit('checkedIds', this.setCheckList(this.checkedKeys,this.newList))
        },
    handleNavigationTwo(checkedKey, first, second, id) {
        const result = checkedKey.includes(id)
        if (result) {
        const results = checkedKey.includes(second.name)
        if (!results) {
            this.checkedKeys.push(second.name)
        }
        } else {
        if (second.name === id) {
            // 存在选择中导航的数据 进行删除, 反之 不进行操作 nDid
            for (let j = 0, lens = this.checkedKeys.length; j < lens; j++) {
            // 进行是否存在的判断
            const results = this.checkedKeys[j] === (second.name)
            if (results) {
                this.checkedKeys.splice(j, 1)
            }
            }
        }
        }
        const accountArr = []
        first.webSiteAndMediaAccountList.map((item) => { accountArr.push(item.name) })
        const arr = accountArr.filter(e => this.checkedKeys.includes(e))
        if (arr.length == 0) {
            this.checkedKeys.splice(this.checkedKeys.findIndex(item => item === first.name), 1)
        }
        if (arr.length == first.webSiteAndMediaAccountList.length) {
            this.checkedKeys.push(first.name)
        }
        first.isIndeterminate = arr.length > 0 && arr.length < first.webSiteAndMediaAccountList.length
        this.isIndeterminate = this.checkedKeys.length > 0 && this.checkedKeys.length < this.filterLength.length
        this.$forceUpdate()
        this.$emit('checkedIds', this.setCheckList(this.checkedKeys,this.newList))
        },
        resetCheck(){
            this.activeName = '0'
            this.checkedKeys = []
            this.checkAll = false
            this.isIndeterminate = false
            this.newList.map((first)=>{
                first.isIndeterminate = false
            })
            this.$emit('checkedIds',[])
            this.$forceUpdate()
        },
        // 设置选中的站点
        setCheckedSites(siteNames, retryCount = 0) {
            if (!siteNames || siteNames.length === 0) {
                this.resetCheck();
                return;
            }

            // 最大重试次数
            const maxRetries = 10;

            // 等待数据加载完成
            this.$nextTick(() => {
                // 检查数据是否已加载完成
                if (this.loading || this.newList.length === 0) {
                    if (retryCount < maxRetries) {
                        // 如果数据还没加载完成，延迟重试
                        setTimeout(() => {
                            this.setCheckedSites(siteNames, retryCount + 1);
                        }, 300);
                    } else {
                        console.warn('SitePage数据加载超时，无法设置选中状态');
                    }
                    return;
                }

                // 清空当前选中状态
                this.checkedKeys = [];
                this.checkAll = false;
                this.isIndeterminate = false;

                let foundMatch = false;

                // 遍历站点数据，查找匹配的站点
                this.newList.forEach((first) => {
                    first.isIndeterminate = false;
                    let hasCheckedChild = false;
                    let checkedChildCount = 0;

                    // 检查子站点
                    first.webSiteAndMediaAccountList.forEach((second) => {
                        // 支持多种匹配方式：name、nickName
                        const isMatch = siteNames.some(siteName =>
                            siteName === second.name ||
                            siteName === second.nickName ||
                            siteName === second.accountName
                        );

                        if (isMatch) {
                            if (!this.checkedKeys.includes(second.name)) {
                                this.checkedKeys.push(second.name);
                            }
                            hasCheckedChild = true;
                            checkedChildCount++;
                            foundMatch = true;
                        }
                    });

                    // 如果所有子站点都被选中，则选中父级
                    if (checkedChildCount === first.webSiteAndMediaAccountList.length && checkedChildCount > 0) {
                        if (!this.checkedKeys.includes(first.name)) {
                            this.checkedKeys.push(first.name);
                        }
                    } else if (hasCheckedChild) {
                        // 部分选中，设置半选状态
                        first.isIndeterminate = true;
                    }
                });

                // 更新全选状态
                if (this.checkedKeys.length === this.filterLength.length) {
                    this.checkAll = true;
                    this.isIndeterminate = false;
                } else if (this.checkedKeys.length > 0) {
                    this.isIndeterminate = true;
                }

                // 触发选中事件
                this.$emit('checkedIds', this.setCheckList(this.checkedKeys, this.newList));
                this.$forceUpdate();

                // 如果没有找到匹配的站点，输出警告
                if (!foundMatch) {
                    console.warn('未找到匹配的站点:', siteNames);
                }
            });
        }
    },
    created(){
        this.queryMediaSite()
    }
}
</script>
<style scoped lang="scss">
.mag-wrap{
  max-height: 150px;
  overflow-y: auto;
}
.msg-group{
  display:inline-block;
  width: 240px;
  margin: 0 0 0 20px;
}
</style>